import re
import os

def extract_all_sections(file_path, output_dir):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Comprehensive list of sections found in the document
    all_sections = [
        # Chapter 1 sections
        ('1.1.1', 'The Homestay Sector as a Critical Lever for Rural Revitalisation and Sustainable Development'),
        ('1.1.2', 'Robust Recovery of the Global Vacation Rental (Homestay) Market'),
        ('1.1.3', 'Rapid Development of China\'s Homestay (Minsu) Market'),
        ('1.1.4', 'Current State of Homestay Development in Guizhou'),
        ('1.1.5', 'Current Challenges Confronting Guizhou\'s Homestay Sector'),
        ('1.2.1', 'Competitive Advantage in the Homestay Sector'),
        ('1.2.2', 'The Role of Management Innovation in the Homestay Sector'),
        ('1.2.3', 'Sustainable Development Strategies for Homestays'),
        ('1.2.4', 'Innovation–Sustainability Synergy as a Source of Competitive Advantage'),
        ('1.2.5', 'Theoretical Foundations and Methodological Pathways'),
        ('1.3.1', 'Theoretical Gaps'),
        ('1.3.2', 'Mechanistic and Boundary Gaps'),
        ('1.3.3', 'Thematic and Contextual Gaps'),
        ('1.3.4', 'Insufficient Localised Research in China'),
        ('1.6.1', 'Theoretical contributions'),
        ('1.6.2', 'Methodological contributions'),
        ('1.6.3', 'Empirical contributions'),
        ('1.6.4', 'Policy implications'),
        
        # Chapter 2 sections
        ('2.1.1', 'Market Based View (MBV): An Industry Structure Perspective'),
        ('2.1.2', 'The Resource Based View (RBV): A Focus on Resource Heterogeneity'),
        ('2.1.3', 'The Integrative Perspective: Market Commonality × Resource Similarity'),
        ('2.1.4', 'Dynamic Capabilities View (DCV): Embedding the Temporal Dimension'),
        ('2.1.5', 'The Value Network Perspective: Platforms and Network Effects'),
        ('2.1.6', 'Interim Synthesis and Insights'),
        ('2.2.1', 'Assimilation Perspective: The Technological Efficiency Paradigm'),
        ('2.2.2', 'Segmentation Perspective: The IHIP Interaction Paradigm'),
        ('2.2.3', 'Integrative Perspective: Fusing Characteristics, Competences and Systems'),
        ('2.2.4', 'Open Service Innovation × Service Dominant Logic (OSI × SDL)'),
        ('2.2.5', 'A Three Dimensional Flywheel: Technology, Interaction, and Sustainability'),
        ('2.3.1', 'The Triple Bottom Line (TBL): Integrating Economic, Social, and Environmental Dimensions'),
        ('2.3.2', 'A Six Dimensional Framework of Community Empowerment: Economic, Social, Psychological, Political, Cultural, and Environmental'),
        ('2.3.3', 'Homestays as a Vehicle for Community Based Tourism and Rural Revitalization'),
        ('2.3.4', 'Summary: Implications of TBL and Empowerment for Competitive Advantage'),
        ('2.4.1', 'Literature Review: Horizontal Fragmentation, Vertical Disconnection, and Contextual Deficiency'),
        ('2.4.2', 'The Need for an RBV–DCV–TBL Synthesis and Related Research Opportunities'),
        ('2.5.1', 'Dynamic Capabilities (DC)'),
        ('2.5.2', 'Relational Coordination (RC)'),
        ('2.5.3', 'Service Innovation Capability (SIC): A 13 Item, Five Dimension Scale'),
        ('2.5.4', 'Triple Bottom Line Performance (TBL/SP)'),
        ('2.5.5', 'Competitive Advantage (CA)'),
        ('2.5.6', 'Summary: Variable System and Measurement Principles'),
        ('2.6.1', 'Resource–Innovation Pathway (H1–H2)'),
        ('2.6.2', 'Innovation–Performance Pathways (H3a–H3c)'),
        ('2.6.3', 'Performance–Advantage Pathway (H4a–H4c)'),
        ('2.6.4', 'Sequential Mediation and Direct Effects (H5–H6)'),
        ('2.6.5', 'Contextual Moderating Effects (H7)'),
        ('2.6.6', 'Section Summary'),
        
        # Chapter 3 sections  
        ('3.1.1', 'Philosophical Position and Research Paradigm (Empirical Deductive, Hypothesis Testing Orientation)'),
        ('3.1.2', 'Logical Bridge from Theory to Empirics and Analytic Workflow'),
        ('3.1.3', 'Multi method Integration (Survey × Case Interviews × Platform Big Data)'),
        ('3.2.1', 'Regional Selection: The Community Based Homestay Ecosystem in Guizhou and Adjacent Provinces'),
        ('3.2.2', 'Industry Context and the Stakeholder Landscape (Hosts, Communities, Platforms, Government)'),
        ('3.2.3', 'Units of Analysis and Levels of Inquiry: A Multi Level Enterprise–Community–Platform Perspective'),
        ('3.3.1', 'Quantitative Sample: Sampling Frame, Sample Size Estimation, and Stratification Strategy'),
        ('3.3.2', 'Qualitative Sample: Selection of Typical Cases and Evaluation of Data Saturation'),
        ('3.3.3', 'Data Collection Procedures, Timeline, and Ethical Compliance (IRB Review & Informed Consent)'),
        ('3.4.1', 'Dynamic Capabilities (DC): Revised 16‑Item Scale'),
        ('3.4.2', 'Relational Coordination (RC): 9 Item Scale (Including Community Co Creation Dimension)'),
        ('3.4.3', 'Service Innovation Capability (SIC): A 13 Item, Five Dimension Scale'),
        ('3.4.4', 'Triple Bottom Line Performance (TBL/SP): A Nine Item, Three Dimensional Indicator System'),
        ('3.4.5', 'Competitive Advantage (CA): Eight Item, Three Dimension Scale'),
        ('3.4.6', 'Moderating Variables: Environmental Dynamism (ED) and Market Competition Intensity (MCI)'),
        ('3.5.1', 'Expert Review, Cognitive Interviewing, and Item Revision'),
        ('3.5.2', 'Pilot Study'),
        ('3.6.1', 'Missing Data, Outliers, and Normality Diagnostics'),
        ('3.6.2', 'Common Method Bias Assessment (Harman\'s Single Factor Test and the Unmeasured Latent Method Construct)'),
        ('3.6.3', 'Multicollinearity and Scale Balance Diagnostics'),
        ('3.7.1', 'Measurement Model: Confirmatory Factor Analysis (CFA)'),
        ('3.7.2', 'Structural model: AMOS/Smart PLS path analysis and bootstrap mediation testing'),
        ('3.7.3', 'Multi Group and Interaction Effects: Moderation Tests and Critical Ratio Difference Analysis'),
        ('3.7.4', 'Robustness Checks: Alternative Models, Instrumental Variable SEM, and Time Lagged Samples')
    ]
    
    created_files = []
    
    for i, (section_num, section_title) in enumerate(all_sections):
        # Find the section in content
        search_text = section_num + ' ' + section_title
        section_start = content.find(search_text)
        
        if section_start == -1:
            print(f'Section not found: {section_num} {section_title}')
            continue
            
        # Find the next section to determine end
        next_section_start = len(content)
        for j in range(i + 1, len(all_sections)):
            next_num, next_title = all_sections[j]
            next_search = next_num + ' ' + next_title
            next_pos = content.find(next_search, section_start + len(search_text))
            if next_pos != -1 and next_pos < next_section_start:
                next_section_start = next_pos
                break
        
        # Extract section content
        section_content = content[section_start:next_section_start].strip()
        
        # Clean filename
        safe_title = re.sub(r'[^\w\s-]', '', section_title)
        safe_title = re.sub(r'\s+', '_', safe_title)[:60]
        filename = f'{section_num}_{safe_title}.txt'
        
        # Write file
        output_path = os.path.join(output_dir, filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'Section {section_num}: {section_title}\n')
            f.write('=' * 80 + '\n\n')
            f.write(section_content)
        
        created_files.append(filename)
        print(f'Created: {filename}')
    
    return len(created_files)

if __name__ == "__main__":
    input_file = '/mnt/c/Users/<USER>/OneDrive/Desktop/数据分析/论文/1th_content.txt'
    output_directory = '/mnt/c/Users/<USER>/OneDrive/Desktop/数据分析/论文/sections'
    
    total = extract_all_sections(input_file, output_directory)
    print(f'\nTotal files created: {total}')